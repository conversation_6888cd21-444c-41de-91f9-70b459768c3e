from fastapi import APIRouter, Body, Depends, HTTPException, Path, Query
from sqlalchemy.orm import Session

from app.core.exceptions import (
    AIServiceException,
    DatabaseException,
    ResourceNotFoundException,
    SpeechJobInvalidStatusException,
    SpeechJobNotFoundException,
)
from app.db.session import get_db
from app.models.models import TntStudent
from app.schemas.scene import (
    SceneBasicResponse,
    SceneCommentsRequest,
    SceneSpeechBatchDeleteRequest,
    SceneSpeechBatchDeleteResponse,
    SceneSpeechCreateResponse,
    SceneSpeechJobRequest,
    SceneSpeechJobsResponse,
    SceneSpeechRequest,
)
from app.services.auth import get_current_user
from app.services.scene import (
    batch_delete_scene_speeches,
    create_scene_comments,
    create_scene_speech,
    create_scene_speech_jobs,
    get_scene_basic_info,
    get_scene_speech_jobs,
    process_speech_job,
)

router = APIRouter()


@router.get("/{id}", response_model=SceneBasicResponse)
async def get_scene_api(
    id: int = Path(..., description="场景ID"),
    class_id: int = Query(..., description="班级ID"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取场景基本信息

    ## 功能描述
    获取指定场景的基本信息，包括练习状态、时间信息、角色列表、指南列表和发言列表等。

    ## 请求参数
    - **id** (int): 场景ID，通过路径参数传递
    - **class_id** (int): 班级ID，通过查询参数传递

    ## 响应
    - **200**: 成功返回场景基本信息
        - 返回类型: SceneBasicResponse
        - 包含场景的基本信息：
            - title: 标题
            - pic: 图片（OSS签名URL或null）
            - intro: 简介
            - duration: 时长（分钟）
            - bgtext: 背景文字
            - bgvideo: 背景视频URL（OSS签名URL或null）
            - report: 整体点评报告URL（OSS签名URL或null）
            - btime: 开始练习时间
            - stime: 提交练习时间
            - utime: 上次练习时间
            - status: 练习状态（0：待练习；1：练习中；2：已提交）
            - eid: 练习ID
            - elid: 练习情况ID（可为null）
            - tid: 老师ID（可为null）
            - tname: 老师姓名（可为null）
            - tavatar: 老师头像URL（OSS签名URL或null）
            - characters: 角色列表
                - id: 角色ID
                - name: 姓名
                - gender: 性别（0：未知；1：男；2：女）
                - avatar: 头像URL（OSS签名URL或null）
                - profile: 人物资料
                - timbre_type: 音色类型（0：未设置；1：火山引擎）
                - timbre: 音色
                - played: 是否是学员扮演（0：否；1：是）
            - guides: 指南列表（按priority从小到大排序）
                - title: 指南标题
                - details: 指南详情
            - speeches: 场景练习情况（发言）列表（按发言时间排序）
                - id: 发言ID
                - cid: 角色ID
                - played: 是否是学员扮演（0：否；1：是）
                - content: 发言内容
                - to_cids: @列表（角色ID列表，用逗号分隔，可为空字符串）
                - status: 发言状态（0：未处理；1：处理中；2：已处理）
                - ctime: 发言时间

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能查看当前租户下的场景

    ## 业务规则
    - 只能访问当前租户下的场景
    - 只能访问有效的（active=1）练习
    - 角色按scene_character表中的priority字段从小到大排序
    - 指南按scene_guide表中的priority字段从小到大排序
    - 发言按发言时间从早到晚排序

    ## 错误处理
    - **404**: 场景不存在或无权访问
        - 场景ID不存在
        - 无权访问该场景（不属于当前租户）
        - 关联的练习已被删除或无效
    """
    result = get_scene_basic_info(db, id, class_id, current_user)

    if not result:
        raise HTTPException(status_code=404, detail="场景不存在")

    return result


@router.post("/speech", response_model=SceneSpeechCreateResponse)
async def create_speech_api(
    speech_data: SceneSpeechRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    创建学员发言

    ## 功能描述
    在场景练习中创建一条学员发言记录，记录到tnt_scene_speech表中。

    ## 请求参数
    - **elid** (int): 练习情况ID
    - **cid** (int): 角色ID
    - **played** (int): 是否是学员扮演（0：否；1：是）
    - **content** (str): 发言内容
    - **to_cids** (str, optional): @列表（角色ID列表，用逗号分隔，可为空字符串）

    ## 响应
    - **200**: 成功创建发言
        - 返回类型: SceneSpeechCreateResponse
        - 包含发言的基本信息：
            - id: 发言ID
            - status: 发言状态（0：未处理；1：处理中；2：已处理）
            - elid: 练习情况ID

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能在自己的练习情况中创建发言
    - 只能使用当前租户下的角色

    ## 业务规则
    - 练习情况必须存在且属于当前用户
    - 角色必须存在且属于当前租户
    - 发言状态默认为0（未处理）
    - 发言时间自动设置为当前时间

    ## 错误处理
    - **404**: 练习情况不存在或无权访问
        - 练习情况ID不存在
        - 无权访问该练习情况（不属于当前用户）
        - 关联的练习已被删除或无效
    - **400**: 角色不存在或无权使用
        - 角色ID不存在
        - 角色不属于当前租户
        - 角色已被删除或无效
    - **500**: 服务器内部错误
        - 数据库操作失败
    """
    try:
        return create_scene_speech(db, speech_data, current_user)
    except ResourceNotFoundException as e:
        # 业务异常：资源不存在，返回404
        raise HTTPException(status_code=404, detail=e.message)
    except DatabaseException as e:
        # 数据库异常：让中间件处理，会返回500并记录详细日志
        raise e


@router.post("/speech/{id}/jobs", response_model=SceneSpeechJobsResponse)
async def create_speech_jobs_api(
    id: int = Path(..., description="发言ID"),
    job_data: SceneSpeechJobRequest = Body(..., description="任务数据"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    创建发言任务

    ## 功能描述
    根据发言ID和练习情况ID创建发言任务，通过AI分析发言内容并生成相应的任务列表。

    ## 请求参数
    - **id** (int): 发言ID，通过路径参数传递
    - **elid** (int): 练习情况ID，通过请求体传递

    ## 响应
    - **200**: 成功创建任务列表
        - 返回类型: SceneSpeechJobsResponse
        - 包含任务列表：
            - jobs: 任务列表
                - id: 任务ID
                - cid: 完成任务的角色ID
                - status: 任务状态（0：未开始；1：进行中；2：已完成）
                - sync: 同步或异步（0：异步；1：同步）
                - ctime: 创建时间

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能处理当前租户下的发言和练习情况

    ## 业务规则
    1. 根据发言ID和练习情况ID查找未处理的发言记录（played=1且status=0）
    2. 更新发言状态为处理中（status=1）
    3. 采集相关数据：会议背景、会议记录、参会人员、调度规则、发言者信息
    4. 调用AI服务分析发言内容，获取角色分配结果
    5. 根据AI结果和调度规则创建任务记录
    6. 更新发言状态为已处理（status=2）并返回任务列表

    ## 错误处理
    - **404**: 发言不存在或无权访问
        - 发言ID不存在
        - 发言不满足处理条件（played!=1或status!=0）
        - 无权访问该发言（不属于当前租户）
        - 关联的练习情况不存在
    - **500**: 服务器内部错误
        - AI服务调用失败
        - 数据库操作失败
        - 机器人配置不存在

    ## 注意事项
    - 如果处理过程中发生错误，会自动回滚发言状态到未处理（status=0）
    - 任务的同步/异步属性由调度规则中的serial字段决定
    - 只有在to_cids列表中的角色才会被分配任务（如果to_cids为空则不过滤）
    """
    try:
        return await create_scene_speech_jobs(db, id, job_data, current_user)
    except ResourceNotFoundException as e:
        # 业务异常：资源不存在，返回404
        raise HTTPException(status_code=404, detail=e.message)
    except AIServiceException as e:
        # AI服务异常：让中间件处理，会返回500并记录详细日志
        raise e
    except DatabaseException as e:
        # 数据库异常：让中间件处理，会返回500并记录详细日志
        raise e


@router.get("/speech/{id}/jobs", response_model=SceneSpeechJobsResponse)
async def get_speech_jobs_api(
    id: int = Path(..., description="发言ID"),
    elid: int = Query(..., description="练习情况ID"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    获取会议发言任务列表

    ## 功能描述
    根据发言ID和练习情况ID获取会议发言任务列表。

    ## 请求参数
    - **id** (int): 发言ID，通过路径参数传递
    - **elid** (int): 练习情况ID，通过查询参数传递

    ## 响应
    - **200**: 成功返回任务列表
        - 返回类型: SceneSpeechJobsResponse
        - 包含任务列表：
            - jobs: 任务列表
                - id: 任务ID
                - cid: 完成任务的角色ID
                - status: 任务状态（0：未开始；1：进行中；2：已完成）
                - sync: 同步或异步（0：异步；1：同步）
                - ctime: 创建时间

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能查看当前租户下的任务

    ## 业务规则
    1. 在tnt_scene_speech中根据id，elid条件以及played=1且status=0获取记录
    2. 如果找到记录，返回400错误（只能获得正确且未处理发言所对应的任务）
    3. 如果没有记录，从tnt_speech_job中根据elid获取job list（id从小到大排序）返回

    ## 错误处理
    - **400**: 找到未处理的发言记录
        - 存在played=1且status=0的发言记录
        - 只能获得正确且未处理发言所对应的任务
    - **404**: 没有找到相关数据
        - 练习情况不存在
        - 无权访问该练习情况（不属于当前租户）
    - **500**: 服务器内部错误
        - 数据库操作失败

    ## 注意事项
    - 任务列表按ID从小到大排序
    - 只返回当前租户下的任务
    """
    result = get_scene_speech_jobs(db, id, elid, current_user)

    if result is None:
        raise HTTPException(
            status_code=400, detail="只能获得正确且未处理发言所对应的任务"
        )

    return result


@router.post("/job/{id}/process")
async def process_job_api(
    id: int = Path(..., description="任务ID"),
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    处理任务

    ## 功能描述
    根据任务ID处理发言任务，通过AI生成角色发言内容并创建新的发言记录。

    ## 请求参数
    - **id** (int): 任务ID，通过路径参数传递

    ## 响应
    - **200**: 成功返回流式响应
        - 返回类型: StreamingResponse
        - 内容类型: text/event-stream
        - 流式返回AI生成的发言内容

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能处理当前租户下的任务

    ## 业务规则
    1. 判断任务状态，只有status=0的任务才能处理
    2. 将任务状态更新为1（进行中）
    3. 根据任务中的发言ID获取原始发言内容
    4. 调用AI服务生成角色发言
    5. 成功后创建新的发言记录并删除任务
    6. 失败时将任务状态重置为0

    ## 错误处理
    - **404**: 任务不存在或无权访问
        - 任务ID不存在
        - 无权访问该任务（不属于当前租户）
    - **400**: 任务状态不对无法处理
        - 任务状态不为0（未开始）
    - **500**: 服务器内部错误
        - AI服务调用失败
        - 数据库操作失败
        - 机器人配置不存在

    ## 注意事项
    - 处理过程中如果发生错误，会自动回滚任务状态到未开始（status=0）
    - 成功处理后任务记录会被删除
    - 生成的发言记录played=0，status=2
    """
    # 只处理明确的业务异常，其他异常交给中间件处理
    try:
        result = await process_speech_job(db, id, current_user)
        return result
    except SpeechJobNotFoundException:
        raise HTTPException(status_code=404, detail="任务不存在")
    except SpeechJobInvalidStatusException:
        raise HTTPException(status_code=400, detail="任务状态不对无法处理")
    except ResourceNotFoundException:
        raise HTTPException(status_code=404, detail="相关资源不存在")


@router.delete("/speech/batch", response_model=SceneSpeechBatchDeleteResponse)
async def batch_delete_speeches_api(
    delete_data: SceneSpeechBatchDeleteRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    批量删除对话

    ## 功能描述
    批量删除根据tenant_id和elid获取到的对话中，id大于等于from_id的所有数据。

    ## 请求参数
    - **from_id** (int): 对话ID，删除ID大于等于此值的所有对话
    - **elid** (int): 练习情况ID

    ## 响应
    - **200**: 成功删除对话
        - 返回类型: SceneSpeechBatchDeleteResponse
        - 包含删除的对话数量：
            - deleted_count: 删除对话的数量

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能删除当前租户下的对话

    ## 业务规则
    - 批量删除根据tenant_id和elid获取到的对话中，id大于等于from_id的所有数据
    - 在同一个事务中完成删除操作
    - 只能删除当前租户下的对话

    ## 错误处理
    - **500**: 服务器内部错误
        - 数据库操作失败

    ## 注意事项
    - 删除操作在事务中进行，确保数据一致性
    - 返回实际删除的对话数量
    """
    try:
        deleted_count = batch_delete_scene_speeches(
            db, delete_data.from_id, delete_data.elid, current_user
        )
        return SceneSpeechBatchDeleteResponse(deleted_count=deleted_count)
    except DatabaseException as e:
        # 数据库异常：让中间件处理，会返回500并记录详细日志
        raise e


@router.post("/comments")
async def create_comments_api(
    comments_data: SceneCommentsRequest,
    current_user: TntStudent = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    创建整体点评

    ## 功能描述
    根据练习情况ID生成整体点评，通过AI分析发言历史并生成点评内容。

    ## 请求参数
    - **elid** (int): 练习情况ID

    ## 响应
    - **200**: 成功返回流式响应
        - 返回类型: StreamingResponse
        - 内容类型: text/event-stream
        - 流式返回AI生成的整体点评内容

    ## 权限要求
    - 需要有效的用户身份令牌
    - 只能处理当前租户下的练习情况

    ## 业务规则
    1. 根据elid查询tnt_scene_speech记录，按id从小到大排序
    2. 通过cid关联tnt_character表，构造发言历史
    3. 调用AI服务生成整体点评
    4. 成功获取全部整体点评内容后，保存到tnt_exercise_log的comments字段

    ## 错误处理
    - **404**: 练习情况不存在或无权访问
        - 练习情况ID不存在
        - 无权访问该练习情况（不属于当前租户）
    - **500**: 服务器内部错误
        - AI服务调用失败
        - 数据库操作失败
        - 机器人配置不存在

    ## 注意事项
    - 处理过程中如果发生错误，不会保存点评内容
    - 只有在成功获取完整点评内容后才会保存到数据库
    """
    try:
        return await create_scene_comments(db, comments_data, current_user)
    except ResourceNotFoundException as e:
        # 业务异常：资源不存在，返回404
        raise HTTPException(status_code=404, detail=e.message)
    except AIServiceException as e:
        # AI服务异常：让中间件处理，会返回500并记录详细日志
        raise e
    except DatabaseException as e:
        # 数据库异常：让中间件处理，会返回500并记录详细日志
        raise e
